/**
 * AI内容格式测试组件
 * 测试AI生成内容的格式处理和编辑模式切换
 */

import React, { useState } from "react";
import { Button, Card, Space, Typography, Input, Alert, Divider } from "antd";
import { PlayCircleOutlined, CheckCircleOutlined, EditOutlined } from "@ant-design/icons";
import { smartContentConversion, isMarkdownContent } from "../utils/markdownToHtml";
import { parseMarkdownToProseMirror } from "../utils/markdownToProseMirror";

const { Title, Text } = Typography;
const { TextArea } = Input;

/**
 * AI内容格式测试页面
 */
const AIContentFormatTest: React.FC = () => {
  const [testMarkdown, setTestMarkdown] = useState(`# AI生成内容测试

这是一个**AI生成**的便签内容示例。

## 任务列表
- [ ] 未完成的任务
- [x] 已完成的任务
- [ ] 另一个待办事项

## 普通列表
- 第一项
- 第二项
- 第三项

## 有序列表
1. 首先做这个
2. 然后做那个
3. 最后完成

## 表格
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |

## 代码
\`\`\`javascript
function hello() {
  console.log("Hello World!");
}
\`\`\`

> 这是一个引用块

**粗体文本** 和 *斜体文本*`);

  const [htmlOutput, setHtmlOutput] = useState("");
  const [proseMirrorJson, setProseMirrorJson] = useState("");
  const [testResults, setTestResults] = useState<Array<{
    name: string;
    success: boolean;
    message: string;
    details?: any;
  }>>([]);

  // 执行格式转换测试
  const runFormatTest = () => {
    const results: typeof testResults = [];

    try {
      // 1. 测试Markdown检测
      const isMarkdown = isMarkdownContent(testMarkdown);
      results.push({
        name: "Markdown格式检测",
        success: isMarkdown,
        message: isMarkdown ? "✅ 正确识别为Markdown格式" : "❌ 未能识别Markdown格式"
      });

      // 2. 测试Markdown到HTML转换
      const htmlResult = smartContentConversion(testMarkdown);
      setHtmlOutput(htmlResult);
      
      const hasTaskList = htmlResult.includes('data-type="taskList"');
      const hasTable = htmlResult.includes('<table>');
      const hasCodeBlock = htmlResult.includes('<pre><code>');
      
      results.push({
        name: "Markdown到HTML转换",
        success: htmlResult.length > 0,
        message: htmlResult.length > 0 ? "✅ 成功转换为HTML" : "❌ HTML转换失败",
        details: {
          hasTaskList,
          hasTable,
          hasCodeBlock,
          outputLength: htmlResult.length
        }
      });

      // 3. 测试Markdown到ProseMirror JSON转换
      const jsonResult = parseMarkdownToProseMirror(testMarkdown);
      
      if (jsonResult.success && jsonResult.contentJson) {
        setProseMirrorJson(JSON.stringify(jsonResult.contentJson, null, 2));
        
        const hasTaskNodes = JSON.stringify(jsonResult.contentJson).includes('"type":"taskList"');
        const hasTableNodes = JSON.stringify(jsonResult.contentJson).includes('"type":"table"');
        
        results.push({
          name: "Markdown到ProseMirror JSON转换",
          success: true,
          message: "✅ 成功转换为ProseMirror JSON",
          details: {
            hasTaskNodes,
            hasTableNodes,
            nodeCount: jsonResult.contentJson.content?.length || 0
          }
        });
      } else {
        setProseMirrorJson(`转换失败: ${jsonResult.error}`);
        results.push({
          name: "Markdown到ProseMirror JSON转换",
          success: false,
          message: `❌ ProseMirror JSON转换失败: ${jsonResult.error}`
        });
      }

      // 4. 测试编辑模式兼容性
      const editModeTest = htmlResult.length > 0 && jsonResult.success;
      results.push({
        name: "编辑模式兼容性",
        success: editModeTest,
        message: editModeTest 
          ? "✅ HTML和JSON格式都可用，编辑模式切换应该正常"
          : "❌ 格式转换不完整，编辑模式可能有问题"
      });

    } catch (error) {
      results.push({
        name: "测试执行",
        success: false,
        message: `❌ 测试执行失败: ${error instanceof Error ? error.message : "未知错误"}`
      });
    }

    setTestResults(results);
  };

  return (
    <div style={{ padding: "20px", maxWidth: "1400px", margin: "0 auto" }}>
      <Title level={2}>🧪 AI内容格式测试</Title>
      <Text type="secondary">
        测试AI生成内容的格式处理、编辑模式切换和各种Markdown元素的兼容性。
      </Text>

      <Space direction="vertical" size="large" style={{ width: "100%", marginTop: "20px" }}>
        {/* 控制面板 */}
        <Card title="测试控制" size="small">
          <Button 
            type="primary" 
            icon={<PlayCircleOutlined />}
            onClick={runFormatTest}
          >
            执行格式转换测试
          </Button>
        </Card>

        {/* 输入区域 */}
        <Card title="测试输入（模拟AI生成的Markdown内容）" size="small">
          <TextArea
            value={testMarkdown}
            onChange={(e) => setTestMarkdown(e.target.value)}
            rows={12}
            placeholder="输入Markdown内容..."
            style={{ fontFamily: "monospace" }}
          />
        </Card>

        {/* 结果展示 */}
        <div style={{ display: "flex", gap: "20px" }}>
          {/* HTML输出 */}
          <div style={{ flex: 1 }}>
            <Card title="HTML输出（流式显示格式）" size="small">
              <TextArea
                value={htmlOutput}
                rows={8}
                readOnly
                placeholder="HTML输出将显示在这里..."
                style={{ fontFamily: "monospace", backgroundColor: "#f5f5f5" }}
              />
            </Card>
          </div>

          {/* ProseMirror JSON输出 */}
          <div style={{ flex: 1 }}>
            <Card title="ProseMirror JSON（编辑格式）" size="small">
              <TextArea
                value={proseMirrorJson}
                rows={8}
                readOnly
                placeholder="ProseMirror JSON将显示在这里..."
                style={{ fontFamily: "monospace", backgroundColor: "#f5f5f5" }}
              />
            </Card>
          </div>
        </div>

        {/* 测试结果 */}
        {testResults.length > 0 && (
          <Card title="测试结果" size="small">
            <Space direction="vertical" style={{ width: "100%" }}>
              {testResults.map((result, index) => (
                <Alert
                  key={index}
                  message={result.name}
                  description={
                    <div>
                      <div>{result.message}</div>
                      {result.details && (
                        <div style={{ marginTop: "8px", fontSize: "12px", color: "#666" }}>
                          详细信息: {JSON.stringify(result.details, null, 2)}
                        </div>
                      )}
                    </div>
                  }
                  type={result.success ? "success" : "error"}
                  icon={<CheckCircleOutlined />}
                  showIcon
                />
              ))}
            </Space>
          </Card>
        )}

        {/* 测试说明 */}
        <Card title="测试说明" size="small">
          <Space direction="vertical">
            <Text strong>测试目标：</Text>
            <ul style={{ fontSize: "14px", margin: 0 }}>
              <li>验证AI生成的Markdown内容能正确转换为HTML（用于流式显示）</li>
              <li>验证Markdown内容能正确转换为ProseMirror JSON（用于编辑）</li>
              <li>验证任务列表、表格、代码块等复杂格式的兼容性</li>
              <li>验证编辑模式切换时格式保持一致</li>
            </ul>
            
            <Divider />
            
            <Text strong>修复要点：</Text>
            <ul style={{ fontSize: "14px", margin: 0 }}>
              <li>AI生成过程中实时转换Markdown为HTML用于显示</li>
              <li>AI生成完成后转换Markdown为ProseMirror JSON用于编辑</li>
              <li>编辑模式切换时确保使用正确的HTML内容</li>
              <li>支持Tiptap的任务列表、表格等扩展格式</li>
            </ul>
          </Space>
        </Card>
      </Space>
    </div>
  );
};

export default AIContentFormatTest;
