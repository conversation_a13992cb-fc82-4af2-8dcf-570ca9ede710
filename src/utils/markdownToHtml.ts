/**
 * 简单的Markdown到HTML转换工具
 * 用于AI生成过程中的实时内容显示
 * 支持基本的Markdown语法，确保与Tiptap兼容
 */

/**
 * 将Markdown文本转换为HTML
 * 这是一个轻量级的转换器，主要用于流式显示
 * @param markdown Markdown文本
 * @returns HTML字符串
 */
export function markdownToHtml(markdown: string): string {
  if (!markdown || typeof markdown !== "string") {
    return "";
  }

  let html = markdown
    // 处理标题
    .replace(/^### (.*$)/gim, "<h3>$1</h3>")
    .replace(/^## (.*$)/gim, "<h2>$1</h2>")
    .replace(/^# (.*$)/gim, "<h1>$1</h1>")

    // 处理粗体和斜体
    .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
    .replace(/\*(.*?)\*/g, "<em>$1</em>")

    // 处理行内代码
    .replace(/`(.*?)`/g, "<code>$1</code>")

    // 处理链接
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')

    // 处理任务列表 - 与Tiptap兼容的格式
    .replace(
      /^(\s*)- \[ \] (.*)$/gm,
      '$1<ul data-type="taskList"><li data-type="taskItem" data-checked="false">$2</li></ul>'
    )
    .replace(
      /^(\s*)- \[x\] (.*)$/gm,
      '$1<ul data-type="taskList"><li data-type="taskItem" data-checked="true">$2</li></ul>'
    )

    // 处理无序列表
    .replace(/^(\s*)- (.*)$/gm, "$1<ul><li>$2</li></ul>")

    // 处理有序列表
    .replace(/^(\s*)\d+\. (.*)$/gm, "$1<ol><li>$2</li></ol>")

    // 处理代码块
    .replace(
      /```(\w+)?\n([\s\S]*?)```/g,
      '<pre><code class="language-$1">$2</code></pre>'
    )

    // 处理表格（简单实现）
    .replace(/\|(.+)\|/g, (match, content) => {
      const cells = content.split("|").map((cell: string) => cell.trim());
      const cellTags = cells.map((cell: string) => `<td>${cell}</td>`).join("");
      return `<table><tr>${cellTags}</tr></table>`;
    })

    // 处理引用
    .replace(/^> (.*)$/gm, "<blockquote>$1</blockquote>")

    // 处理换行
    .replace(/\n\n/g, "</p><p>")
    .replace(/\n/g, "<br>");

  // 包装在段落标签中
  if (html && !html.startsWith("<")) {
    html = "<p>" + html + "</p>";
  }

  // 清理多余的标签
  html = html
    .replace(/<\/ul>\s*<ul>/g, "") // 合并连续的ul标签
    .replace(/<\/ol>\s*<ol>/g, "") // 合并连续的ol标签
    .replace(/<\/blockquote>\s*<blockquote>/g, "<br>") // 合并连续的引用
    .replace(/<p><\/p>/g, "") // 移除空段落
    .replace(/<p><br><\/p>/g, "<br>"); // 简化只有换行的段落

  return html;
}

/**
 * 清理HTML内容，移除不必要的标签和属性
 * @param html HTML字符串
 * @returns 清理后的HTML字符串
 */
export function cleanHtml(html: string): string {
  if (!html || typeof html !== "string") {
    return "";
  }

  return (
    html
      // 移除危险的标签和属性
      .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, "")
      .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "")
      .replace(/on\w+="[^"]*"/gi, "")
      .replace(/javascript:/gi, "")

      // 清理多余的空白
      .replace(/\s+/g, " ")
      .replace(/>\s+</g, "><")
      .trim()
  );
}

/**
 * 检查内容是否为Markdown格式
 * @param content 内容字符串
 * @returns 是否为Markdown格式
 */
export function isMarkdownContent(content: string): boolean {
  if (!content || typeof content !== "string") {
    return false;
  }

  // 检查常见的Markdown标记
  const markdownPatterns = [
    /^#{1,6}\s+/m, // 标题
    /\*\*.*?\*\*/, // 粗体
    /\*.*?\*/, // 斜体
    /`.*?`/, // 行内代码
    /```[\s\S]*?```/, // 代码块
    /^\s*[-*+]\s+/m, // 无序列表
    /^\s*\d+\.\s+/m, // 有序列表
    /^\s*- \[[x ]\]\s+/m, // 任务列表
    /\[.*?\]\(.*?\)/, // 链接
    /^>\s+/m, // 引用
  ];

  return markdownPatterns.some((pattern) => pattern.test(content));
}

/**
 * 智能内容格式检测和转换
 * @param content 内容字符串
 * @returns 转换后的HTML内容
 */
export function smartContentConversion(content: string): string {
  if (!content || typeof content !== "string") {
    return "";
  }

  // 如果内容看起来像Markdown，则转换为HTML
  if (isMarkdownContent(content)) {
    return markdownToHtml(content);
  }

  // 如果已经是HTML，则清理并返回
  if (content.includes("<") && content.includes(">")) {
    return cleanHtml(content);
  }

  // 纯文本，包装在段落标签中
  return `<p>${content.replace(/\n/g, "<br>")}</p>`;
}
